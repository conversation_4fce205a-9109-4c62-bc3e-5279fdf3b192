{"version": 3, "file": "native-messaging-host.js", "sourceRoot": "", "sources": ["../src/native-messaging-host.ts"], "names": [], "mappings": ";;;AAAA,qCAAwC;AAExC,+BAAoC;AACpC,yDAAsD;AACtD,yCAAsC;AAQtC,MAAa,mBAAmB;IAAhC;QACU,qBAAgB,GAAkB,IAAI,CAAC;QACvC,oBAAe,GAAgC,IAAI,GAAG,EAAE,CAAC;IA0PnE,CAAC;IAxPQ,SAAS,CAAC,cAAsB;QACrC,IAAI,CAAC,gBAAgB,GAAG,cAAc,CAAC;IACzC,CAAC;IAED,+CAA+C;IACxC,KAAK;QACV,IAAI,CAAC;YACH,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAEO,oBAAoB;QAC1B,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,cAAc,GAAG,CAAC,CAAC,CAAC;QAExB,eAAK,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE;YACxB,IAAI,KAAK,CAAC;YACV,OAAO,CAAC,KAAK,GAAG,eAAK,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC;gBACvC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBAExC,IAAI,cAAc,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBAChD,cAAc,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBACxC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC;gBAED,IAAI,cAAc,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,cAAc,EAAE,CAAC;oBAC7D,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;oBACtD,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;oBAEtC,IAAI,CAAC;wBACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;wBACrD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;oBAC9B,CAAC;oBAAC,OAAO,KAAU,EAAE,CAAC;wBACpB,IAAI,CAAC,SAAS,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC9D,CAAC;oBACD,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,yBAAyB;gBAChD,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,eAAK,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACnB,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,eAAK,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACrB,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAY;;QACtC,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC5C,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YACzC,OAAO;QACT,CAAC;QAED,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAChC,MAAM,SAAS,GAAG,OAAO,CAAC,mBAAmB,CAAC;YAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAEpD,IAAI,OAAO,EAAE,CAAC;gBACZ,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAChC,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;oBAClB,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC3C,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACnC,CAAC;gBACD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,cAAc;YAChB,CAAC;YACD,OAAO;QACT,CAAC;QAED,wCAAwC;QACxC,IAAI,CAAC;YACH,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACrB,KAAK,qCAAiB,CAAC,KAAK;oBAC1B,MAAM,IAAI,CAAC,WAAW,CAAC,CAAA,MAAA,OAAO,CAAC,OAAO,0CAAE,IAAI,KAAI,IAAI,CAAC,CAAC;oBACtD,MAAM;gBACR,KAAK,qCAAiB,CAAC,IAAI;oBACzB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;oBACxB,MAAM;gBACR,+FAA+F;gBAC/F,KAAK,qBAAqB;oBACxB,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBAChD,MAAM;gBACR;oBACE,kDAAkD;oBAClD,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;wBACjC,IAAI,CAAC,SAAS,CACZ,iDAAiD,OAAO,CAAC,IAAI,IAAI,SAAS,EAAE,CAC7E,CAAC;oBACJ,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,6BAA6B,CAClC,cAAmB,EACnB,cAAsB,cAAc,EACpC,YAAoB,mBAAQ,CAAC,uBAAuB;QAEpD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,IAAA,SAAM,GAAE,CAAC,CAAC,6BAA6B;YAEzD,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,gCAAgC;gBACxE,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,SAAS,IAAI,CAAC,CAAC,CAAC;YAC9D,CAAC,EAAE,SAAS,CAAC,CAAC;YAEd,0DAA0D;YAC1D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;YAEpE,wCAAwC;YACxC,IAAI,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,WAAW,EAAE,6CAA6C;gBAChE,OAAO,EAAE,cAAc;gBACvB,SAAS,EAAE,SAAS,EAAE,+BAA+B;aACtD,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,IAAY;QACpC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QACD,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;gBACpC,IAAI,CAAC,WAAW,CAAC;oBACf,IAAI,EAAE,qCAAiB,CAAC,KAAK;oBAC7B,OAAO,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE;iBAClD,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAE9C,IAAI,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,qCAAiB,CAAC,cAAc;gBACtC,OAAO,EAAE,EAAE,IAAI,EAAE;aAClB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QACD,IAAI,CAAC;YACH,wCAAwC;YACxC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;gBACrC,IAAI,CAAC,WAAW,CAAC;oBACf,IAAI,EAAE,qCAAiB,CAAC,KAAK;oBAC7B,OAAO,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE;iBAC9C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;YACnC,2FAA2F;YAE3F,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,qCAAiB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,sCAAsC;QACtG,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,OAAY;QAC7B,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACjD,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACrC,YAAY,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACpD,sBAAsB;YACtB,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE;gBACjE,IAAI,GAAG,EAAE,CAAC;oBACR,sEAAsE;gBACxE,CAAC;qBAAM,CAAC;oBACN,8CAA8C;gBAChD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,kDAAkD;YAClD,mEAAmE;YACnE,4GAA4G;QAC9G,CAAC;IACH,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,YAAoB;QACpC,IAAI,CAAC,WAAW,CAAC;YACf,IAAI,EAAE,qCAAiB,CAAC,sBAAsB,EAAE,yBAAyB;YACzE,OAAO,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE;SACnC,CAAC,CAAC;IACL,CAAC;IAID;;OAEG;IACK,OAAO;QACb,8BAA8B;QAC9B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACvC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAChC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;YAC7D,IAAI,CAAC,gBAAgB;iBAClB,IAAI,EAAE;iBACN,IAAI,CAAC,GAAG,EAAE;gBACT,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,GAAG,EAAE;gBACV,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;CACF;AA5PD,kDA4PC;AAED,MAAM,2BAA2B,GAAG,IAAI,mBAAmB,EAAE,CAAC;AAC9D,kBAAe,2BAA2B,CAAC"}